import React, { useState, useEffect } from "react";
import { ChevronRight, Folder, FolderOpen } from "lucide-react";
import { Text } from "@snap/design-system";
import { cn } from "~/lib/utils";
import { useEncryption } from "~/hooks/useEncryption";
import { decryptListItem } from "~/helpers";
import { FolderTreeSkeleton } from "~/components/FolderTreeSkeleton";
import { HierarchicalFolderItem } from "~/types/global";

interface FolderTreeNodeProps {
  folder: HierarchicalFolderItem;
  processedFolder: HierarchicalFolderItem | undefined;
  level: number;
  isExpanded: boolean;
  onSelect: (folderId: string) => void;
  onToggleExpand: (folderId: string) => void;
  excludeFolderId?: string;
  processedFolders: HierarchicalFolderItem[];
  selectedFolderId: string | null;
  expandedFolders: Set<string>;
  renderActionButton?: (folderId: string) => React.ReactNode;
}

function FolderTreeNode({
  folder,
  processedFolder,
  level,
  isExpanded,
  onSelect,
  onToggleExpand,
  excludeFolderId,
  processedFolders,
  selectedFolderId,
  expandedFolders,
  renderActionButton,
}: FolderTreeNodeProps) {
  const [isHovered, setIsHovered] = useState(false);
  const hasChildren = folder.data && folder.data.length > 0;
  const isExcluded = excludeFolderId === folder.folder_id;
  const folderName = processedFolder?.folder_name as string;

  const handleClick = () => {
    if (!isExcluded) {
      onSelect(folder.folder_id);
      if (hasChildren) {
        onToggleExpand(folder.folder_id);
      }
    }
  };

  const handleToggleExpand = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (hasChildren) {
      onToggleExpand(folder.folder_id);
    }
  };

  return (
    <div className={cn("select-none", isExcluded && "opacity-50 cursor-not-allowed")}>
      <div
        className={cn(
          "flex items-center gap-2 py-2 px-3 hover:bg-background/10 transition-colors",
          hasChildren ? "cursor-pointer" : "cursor-default"
        )}
        style={{ paddingLeft: `${level * 20 + 12}px` }}
        onClick={handleClick}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div
          className={cn(
            "flex items-center justify-center w-5 h-5 flex-shrink-0 transition-transform duration-200",
            hasChildren ? "cursor-pointer" : "cursor-default"
          )}
          onClick={handleToggleExpand}
        >
          {hasChildren ? (
            <ChevronRight className={cn(
              "w-5 h-5 transition-all duration-200",
              isExpanded && "rotate-90",
              isHovered && !isExcluded ? "text-accent" : "text-foreground"
            )} />
          ) : (
            <div className="w-5 h-5" />
          )}
        </div>

        {isExpanded && hasChildren ? (
          <FolderOpen size={24} className={cn(
            "w-6 h-6 flex-shrink-0 transition-colors duration-200",
            isHovered && !isExcluded ? "text-accent" : "text-foreground"
          )} />
        ) : (
          <Folder size={24} className={cn(
            "w-6 h-6 flex-shrink-0 transition-colors duration-200",
            isHovered && !isExcluded ? "text-accent" : "text-foreground"
          )} />
        )}

        <Text
          className={cn(
            "truncate flex-1 text-base uppercase transition-colors duration-200",
            isExcluded ? "text-muted-foreground" : (
              isHovered ? "text-accent" : "text-foreground"
            )
          )}
          title={folderName}
        >
          {folderName}
        </Text>

        {renderActionButton && !isExcluded && (
          <div
            className={cn(
              "flex items-center justify-center transition-opacity",
              isHovered ? "opacity-100" : "opacity-0"
            )}
            onClick={(e) => e.stopPropagation()}
          >
            {renderActionButton(folder.folder_id)}
          </div>
        )}
      </div>

      {hasChildren && (
        <div className={cn(
          "overflow-hidden transition-all duration-300 ease-in-out",
          isExpanded ? "max-h-screen opacity-100" : "max-h-0 opacity-0"
        )}>
          {folder.data.map((childFolder) => {
            const childProcessedFolder = processedFolders.find(p => p.folder_id === childFolder.folder_id);
            const isChildExpanded = expandedFolders.has(childFolder.folder_id);
            return (
              <FolderTreeNode
                key={childFolder.folder_id}
                folder={childFolder}
                processedFolder={childProcessedFolder}
                level={level + 1}
                isExpanded={isChildExpanded}
                onSelect={onSelect}
                onToggleExpand={onToggleExpand}
                excludeFolderId={excludeFolderId}
                processedFolders={processedFolders}
                selectedFolderId={selectedFolderId}
                expandedFolders={expandedFolders}
                renderActionButton={renderActionButton}
              />
            );
          })}
        </div>
      )}
    </div>
  );
}

interface FolderTreeExplorerProps {
  folders: HierarchicalFolderItem[];
  selectedFolderId: string | null;
  onFolderSelect: (folderId: string | null) => void;
  excludeFolderId?: string;
  showRootOption?: boolean;
  rootOptionLabel?: string;
  className?: string;
  renderActionButton?: (folderId: string) => React.ReactNode;
  currentItemParentFolderId?: string | null;
}

export function FolderTreeExplorer({
  folders,
  selectedFolderId,
  onFolderSelect,
  excludeFolderId,
  showRootOption = true,
  rootOptionLabel = "Pasta raiz",
  className,
  renderActionButton,
  currentItemParentFolderId,
}: FolderTreeExplorerProps) {
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set());
  const [processedFolders, setProcessedFolders] = useState<HierarchicalFolderItem[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const { decryptData } = useEncryption();
  const shouldShowRootOption = showRootOption && currentItemParentFolderId !== null;

  useEffect(() => {
    const processFolders = async () => {
      setIsProcessing(true);
      try {
        const processed: HierarchicalFolderItem[] = [];
        const processFolder = async (folder: HierarchicalFolderItem): Promise<void> => {
          let folderName: string;

          if (typeof folder.folder_name === 'string') {
            folderName = folder.folder_name;
          } else {
            try {
              const decryptedItem = await decryptListItem(folder, decryptData);
              folderName = (decryptedItem as any).folder_name || `Folder ${folder.folder_id.slice(-8)}`;
            } catch (error) {
              console.warn('Failed to decrypt folder name:', error);
              folderName = `Folder ${folder.folder_id.slice(-8)}`;
            }
          }

          processed.push({
            ...folder,
            folder_name: folderName,
          });

          if (folder.data && Array.isArray(folder.data)) {
            for (const child of folder.data) {
              await processFolder(child);
            }
          }
        };

        for (const folder of folders) {
          await processFolder(folder);
        }

        setProcessedFolders(processed);
      } catch (error) {
        console.error('Error processing folders:', error);
        setProcessedFolders([]);
      } finally {
        setIsProcessing(false);
      }
    };

    if (folders.length > 0) {
      processFolders();
    } else {
      setProcessedFolders([]);
      setIsProcessing(false);
    }
  }, [folders, decryptData]);

  const handleToggleExpand = (folderId: string) => {
    setExpandedFolders(prev => {
      const newSet = new Set(prev);
      if (newSet.has(folderId)) {
        newSet.delete(folderId);
      } else {
        newSet.add(folderId);
      }
      return newSet;
    });
  };

  const handleFolderSelect = (folderId: string) => {
    onFolderSelect(folderId);
  };

  const [isRootHovered, setIsRootHovered] = useState(false);

  const handleRootSelect = () => {
    onFolderSelect(null);
  };

  if (isProcessing) {
    return (
      <FolderTreeSkeleton
        className={className}
      />
    );
  }

  return (
    <div className={cn(" rounded-md bg-neutral-600 py-4", className)}>
      {shouldShowRootOption && (
        <div
          className="flex items-center gap-2 py-2 px-3 cursor-pointer hover:bg-muted/50 transition-colors border-b"
          onClick={handleRootSelect}
          onMouseEnter={() => setIsRootHovered(true)}
          onMouseLeave={() => setIsRootHovered(false)}
        >
          <div className="w-5 h-5" />
          <Folder size={24} className={cn(
            "w-6 h-6 flex-shrink-0 transition-colors duration-200",
            isRootHovered ? "text-accent" : "text-foreground"
          )} />
          <Text
            variant="body-md"
            className={cn(
              "truncate flex-1 text-base transition-colors duration-200",
              isRootHovered ? "text-accent" : "text-foreground"
            )}
          >
            {rootOptionLabel}
          </Text>

          {renderActionButton && (
            <div
              className={cn(
                "flex items-center justify-center transition-opacity",
                isRootHovered ? "opacity-100" : "opacity-0"
              )}
              onClick={(e) => e.stopPropagation()}
            >
              {renderActionButton("root")}
            </div>
          )}
        </div>
      )}

      <div className="max-h-96 overflow-y-auto ">
        {folders.map((folder) => {
          const processedFolder = processedFolders.find(p => p.folder_id === folder.folder_id);
          return (
            <FolderTreeNode
              key={folder.folder_id}
              folder={folder}
              processedFolder={processedFolder}
              level={0}
              isExpanded={expandedFolders.has(folder.folder_id)}
              onSelect={handleFolderSelect}
              onToggleExpand={handleToggleExpand}
              excludeFolderId={excludeFolderId}
              processedFolders={processedFolders}
              selectedFolderId={selectedFolderId}
              expandedFolders={expandedFolders}
              renderActionButton={renderActionButton}
            />
          );
        })}
      </div>
    </div>
  );
}
