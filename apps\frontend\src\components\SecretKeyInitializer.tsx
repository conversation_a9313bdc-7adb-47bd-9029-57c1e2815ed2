import { useEffect } from "react";
import { useSecretKeyActions } from "~/store/secretKeyStore";
import { useUserData, useUserIsVerified, useUserActions } from "~/store/userStore";

export const SecretKeyInitializer = () => {
  const { checkStoredKeyValidity } = useSecretKeyActions();
  const { setIsVerified } = useUserActions();
  const userData = useUserData();
  const isVerified = useUserIsVerified();

  useEffect(() => {
    if (userData && userData.salt) {
      const isKeyValid = checkStoredKeyValidity();

      if (!isKeyValid) {
        setIsVerified(false);
      } else if (!isVerified) {
        setIsVerified(true);
      }
    }
  }, [userData, isVerified, checkStoredKeyValidity, setIsVerified]);

  return null;
};
