import { useEffect } from "react";
import { useSecretKeyActions, useSecretKey, getStoredEncryptionKey } from "~/store/secretKeyStore";
import { useUserData, useUserIsVerified, useUserActions } from "~/store/userStore";

export const SecretKeyInitializer = () => {
  const { restoreStoredKey, clearSecretKey } = useSecretKeyActions();
  const { setIsVerified } = useUserActions();
  const secretKey = useSecretKey();
  const userData = useUserData();
  const isVerified = useUserIsVerified();

  useEffect(() => {
    if (userData && userData.salt) {
      const hasValidStoredKey = getStoredEncryptionKey();

      // Se o secretKey indica uma chave armazenada mas não há chave válida armazenada
      if (secretKey === "__STORED_KEY__" && !hasValidStoredKey) {
        console.log("Chave armazenada expirou, limpando estado");
        clearSecretKey();
        setIsVerified(false);
        return;
      }

      // Se não está verificado, tenta restaurar chave armazenada
      if (!isVerified && hasValidStoredKey) {
        try {
          restoreStoredKey();
          setIsVerified(true);
        } catch (error) {
          console.error("Erro ao restaurar chave armazenada:", error);
        }
      }
    }
  }, [userData, isVerified, secretKey, restoreStoredKey, setIsVerified, clearSecretKey]);

  return null;
};
