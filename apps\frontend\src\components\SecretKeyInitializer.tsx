import { useEffect } from "react";
import { useSecretKeyActions } from "~/store/secretKeyStore";
import { useUserData, useUserIsVerified, useUserActions } from "~/store/userStore";

export const SecretKeyInitializer = () => {
  const { checkStoredKeyValidity } = useSecretKeyActions();
  const { setIsVerified } = useUserActions();
  const userData = useUserData();
  const isVerified = useUserIsVerified();

  useEffect(() => {
    if (userData && userData.salt) {
      const isKeyValid = checkStoredKeyValidity();

      if (!isKeyValid) {
        setIsVerified(false);
      } else if (!isVerified) {
        setIsVerified(true);
      }
    }
  }, [userData, isVerified, checkStoredKeyValidity, setIsVerified]);

  // Verificar se a chave armazenada expirou a cada 30 segundos
  useEffect(() => {
    if (!userData) return;

    const interval = setInterval(() => {
      const isKeyValid = checkStoredKeyValidity();
      if (!isKeyValid && isVerified) {
        setIsVerified(false);
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [userData, isVerified, checkStoredKeyValidity, setIsVerified]);

  return null;
};
