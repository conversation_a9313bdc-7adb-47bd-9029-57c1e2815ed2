import { create } from "zustand";

export type StorageDuration = "1m" | "15m" | "30m" | "1h" | "session";

interface StoredKeyData {
  key: string;
  expiresAt: number;
  duration: StorageDuration;
}

interface SecretKeyState {
  secretKey: string | null;
  storageDuration: StorageDuration | null;
  actions: {
    setSecretKey: (key: string, duration?: StorageDuration, salt?: string) => void;
    clearSecretKey: () => void;
    restoreStoredKey: () => void;
  };
}

const STORAGE_KEY = "enc_key";

const getDurationInMs = (duration: StorageDuration): number => {
  switch (duration) {
    case "1m": return 1 * 60 * 1000;
    case "15m": return 15 * 60 * 1000;
    case "30m": return 30 * 60 * 1000;
    case "1h": return 60 * 60 * 1000;
    case "session": return 0; // sessionStorage
  }
};

const arrayBufferToBase64 = (buffer: ArrayBuffer): string => {
  const bytes = new Uint8Array(buffer);
  let binary = '';
  for (let i = 0; i < bytes.byteLength; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return btoa(binary);
};

const base64ToArrayBuffer = (base64: string): ArrayBuffer => {
  const binary = atob(base64);
  const bytes = new Uint8Array(binary.length);
  for (let i = 0; i < binary.length; i++) {
    bytes[i] = binary.charCodeAt(i);
  }
  return bytes.buffer;
};

const storeEncryptionKey = async (key: string, salt: string, duration: StorageDuration): Promise<void> => {
  try {
    const { deriveEncryptionKeyArgon2 } = await import("~/helpers/encryption.helper");
    const cryptoKey = await deriveEncryptionKeyArgon2(key, salt);
    const rawKey = await crypto.subtle.exportKey("raw", cryptoKey);
    const b64Key = arrayBufferToBase64(rawKey);

    const data: StoredKeyData = {
      key: b64Key,
      expiresAt: duration === "session" ? 0 : Date.now() + getDurationInMs(duration),
      duration
    };

    const storage = duration === "session" ? sessionStorage : localStorage;
    storage.setItem(STORAGE_KEY, JSON.stringify(data));
  } catch (error) {
    console.error("Erro ao armazenar chave:", error);
  }
};

const getStoredEncryptionKey = (): string | null => {
  try {
    const sessionData = sessionStorage.getItem(STORAGE_KEY);
    const localData = localStorage.getItem(STORAGE_KEY);

    // Prioriza sessionStorage se existir
    const dataStr = sessionData || localData;
    if (!dataStr) return null;

    const data: StoredKeyData = JSON.parse(dataStr);

    // Verifica expiração (0 = session, nunca expira enquanto tab estiver aberta)
    if (data.expiresAt !== 0 && Date.now() > data.expiresAt) {
      // Remove chave expirada
      sessionStorage.removeItem(STORAGE_KEY);
      localStorage.removeItem(STORAGE_KEY);
      return null;
    }

    return data.key;
  } catch (error) {
    console.error("Erro ao recuperar chave armazenada:", error);
    return null;
  }
};

const clearStoredKey = (): void => {
  sessionStorage.removeItem(STORAGE_KEY);
  localStorage.removeItem(STORAGE_KEY);
};

const useSecretKeyStore = create<SecretKeyState>((set, get) => ({
  secretKey: null,
  storageDuration: null,
  actions: {
    setSecretKey: (key: string, duration?: StorageDuration, salt?: string) => {
      set((state) => ({
        ...state,
        secretKey: key,
        storageDuration: duration || null,
      }));

      // Store the key if duration is specified and salt is available
      if (duration && salt) {
        storeEncryptionKey(key, salt, duration);
      }
    },
    clearSecretKey: () => {
      set({ secretKey: null, storageDuration: null });
      clearStoredKey();
    },
    restoreStoredKey: async () => {
      const storedKey = getStoredEncryptionKey();
      if (storedKey) {
        try {
          // Reconstrói a chave CryptoKey
          const rawKey = base64ToArrayBuffer(storedKey);
          const cryptoKey = await crypto.subtle.importKey(
            "raw",
            rawKey,
            { name: "AES-GCM" },
            false,
            ["encrypt", "decrypt"]
          );

          // Armazena indicador de que temos uma chave válida restaurada
          set({ secretKey: "__STORED_KEY__", storageDuration: "session" });
        } catch (error) {
          console.error("Erro ao restaurar chave:", error);
          clearStoredKey();
        }
      }
    },
  },
}));

export const useSecretKey = () => useSecretKeyStore((state) => state.secretKey);
export const useStorageDuration = () => useSecretKeyStore((state) => state.storageDuration);
export const useSecretKeyActions = () => useSecretKeyStore((state) => state.actions);

// Export das funções para uso em outros locais
export { getStoredEncryptionKey, base64ToArrayBuffer };
