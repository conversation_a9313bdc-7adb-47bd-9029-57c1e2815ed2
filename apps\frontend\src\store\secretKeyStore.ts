import { create } from "zustand";

export type StorageDuration = "1m" | "15m" | "30m" | "1h" | "session";

interface StoredKeyData {
  key: string;
  expiresAt: number;
  duration: StorageDuration;
}

interface SecretKeyState {
  hasValidStoredKey: boolean;
  storageDuration: StorageDuration | null;
  actions: {
    setStoredKey: (key: string, duration: StorageDuration, salt: string) => Promise<boolean>;
    clearStoredKey: () => void;
    checkStoredKeyValidity: () => boolean;
  };
}

const STORAGE_KEY = "enc_key";

const getDurationInMs = (duration: StorageDuration): number => {
  switch (duration) {
    case "1m": return 1 * 60 * 1000;
    case "15m": return 15 * 60 * 1000;
    case "30m": return 30 * 60 * 1000;
    case "1h": return 60 * 60 * 1000;
    case "session": return 0; // sessionStorage
  }
};

const arrayBufferToBase64 = (buffer: ArrayBuffer): string => {
  const bytes = new Uint8Array(buffer);
  let binary = '';
  for (let i = 0; i < bytes.byteLength; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return btoa(binary);
};

const base64ToArrayBuffer = (base64: string): ArrayBuffer => {
  const binary = atob(base64);
  const bytes = new Uint8Array(binary.length);
  for (let i = 0; i < binary.length; i++) {
    bytes[i] = binary.charCodeAt(i);
  }
  return bytes.buffer;
};

const storeEncryptionKey = async (key: string, salt: string, duration: StorageDuration): Promise<void> => {
  try {
    const { deriveEncryptionKeyArgon2 } = await import("~/helpers/encryption.helper");
    const cryptoKey = await deriveEncryptionKeyArgon2(key, salt);
    const rawKey = await crypto.subtle.exportKey("raw", cryptoKey);
    const b64Key = arrayBufferToBase64(rawKey);

    const data: StoredKeyData = {
      key: b64Key,
      expiresAt: duration === "session" ? 0 : Date.now() + getDurationInMs(duration),
      duration
    };

    const storage = duration === "session" ? sessionStorage : localStorage;
    storage.setItem(STORAGE_KEY, JSON.stringify(data));
  } catch (error) {
    console.error("Erro ao armazenar chave:", error);
  }
};

const getStoredEncryptionKey = (): string | null => {
  try {
    const sessionData = sessionStorage.getItem(STORAGE_KEY);
    const localData = localStorage.getItem(STORAGE_KEY);

    // Prioriza sessionStorage se existir
    const dataStr = sessionData || localData;
    if (!dataStr) return null;

    const data: StoredKeyData = JSON.parse(dataStr);

    // Verifica expiração (0 = session, nunca expira enquanto tab estiver aberta)
    if (data.expiresAt !== 0 && Date.now() > data.expiresAt) {
      // Remove chave expirada
      sessionStorage.removeItem(STORAGE_KEY);
      localStorage.removeItem(STORAGE_KEY);
      return null;
    }

    return data.key;
  } catch (error) {
    console.error("Erro ao recuperar chave armazenada:", error);
    return null;
  }
};

const clearStoredKey = (): void => {
  sessionStorage.removeItem(STORAGE_KEY);
  localStorage.removeItem(STORAGE_KEY);
};

const useSecretKeyStore = create<SecretKeyState>((set, get) => ({
  hasValidStoredKey: false,
  storageDuration: null,
  actions: {
    setStoredKey: async (key: string, duration: StorageDuration, salt: string): Promise<boolean> => {
      try {
        await storeEncryptionKey(key, salt, duration);
        set({ hasValidStoredKey: true, storageDuration: duration });
        return true;
      } catch (error) {
        console.error("Erro ao armazenar chave:", error);
        set({ hasValidStoredKey: false, storageDuration: null });
        return false;
      }
    },
    clearStoredKey: () => {
      clearStoredKey();
      set({ hasValidStoredKey: false, storageDuration: null });
    },
    checkStoredKeyValidity: (): boolean => {
      const storedKey = getStoredEncryptionKey();
      const isValid = !!storedKey;
      set({ hasValidStoredKey: isValid });
      return isValid;
    },
  },
}));

export const useHasValidStoredKey = () => useSecretKeyStore((state) => state.hasValidStoredKey);
export const useStorageDuration = () => useSecretKeyStore((state) => state.storageDuration);
export const useSecretKeyActions = () => useSecretKeyStore((state) => state.actions);

// Utility function to check and ensure valid stored key throughout the app
export const ensureValidStoredKey = (): boolean => {
  const storedKey = getStoredEncryptionKey();
  const isValid = !!storedKey;

  // Update store state if needed
  const currentState = useSecretKeyStore.getState();
  if (currentState.hasValidStoredKey !== isValid) {
    useSecretKeyStore.setState({ hasValidStoredKey: isValid });
  }

  return isValid;
};

// Export das funções para uso em outros locais
export { getStoredEncryptionKey, base64ToArrayBuffer };
