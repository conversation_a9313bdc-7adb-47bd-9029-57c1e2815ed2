enum DocumentType {
  CPF = 'CPF',
  CNPJ = 'CNPJ',
  UNKNOWN = 'UNKNOWN'
}

export const maskDocumentNumber = (documentNumber: string): string => {
  if (!documentNumber || typeof documentNumber !== 'string') {
    return '';
  }

  const cleanDocument = sanitizeDocumentNumber(documentNumber);

  if (isAlreadyMasked(documentNumber)) {
    return documentNumber;
  }

  const documentType = getDocumentType(cleanDocument);

  switch (documentType) {
    case DocumentType.CPF:
      return maskCPF(cleanDocument);
    case DocumentType.CNPJ:
      return maskCNPJ(cleanDocument);
    default:
      return documentNumber;
  }
};

/**
 * Removes all non-numeric characters from a string
 * @param document - The document string to sanitize
 * @returns Clean numeric string
 */
const sanitizeDocumentNumber = (document: string): string => {
  return document.replace(/\D/g, '');
};

/**
 * Checks if a document number is already masked
 * @param document - The document string to check
 * @returns True if already masked, false otherwise
 */
const isAlreadyMasked = (document: string): boolean => {
  const cpfMaskPattern = /^\d{3}\.\d{3}\.\d{3}-\d{2}$/;
  const cnpjMaskPattern = /^\d{2}\.\d{3}\.\d{3}\/\d{4}-\d{2}$/;

  return cpfMaskPattern.test(document) || cnpjMaskPattern.test(document);
};

/**
 * Determines the type of document based on its length
 * @param cleanDocument - The sanitized document number
 * @returns The document type enum
 */
const getDocumentType = (cleanDocument: string): DocumentType => {
  if (cleanDocument.length === 11) {
    return DocumentType.CPF;
  }

  if (cleanDocument.length === 14) {
    return DocumentType.CNPJ;
  }

  return DocumentType.UNKNOWN;
};

/**
 * Applies CPF mask formatting (XXX.XXX.XXX-XX)
 * @param cpf - The clean CPF string (11 digits)
 * @returns Formatted CPF string
 */
const maskCPF = (cpf: string): string => {
  return cpf.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
};

/**
 * Applies CNPJ mask formatting (XX.XXX.XXX/XXXX-XX)
 * @param cnpj - The clean CNPJ string (14 digits)
 * @returns Formatted CNPJ string
 */
const maskCNPJ = (cnpj: string): string => {
  return cnpj.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, '$1.$2.$3/$4-$5');
};
