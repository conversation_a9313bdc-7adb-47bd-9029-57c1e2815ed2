import { RELACOES_VINCULOS } from "../config/constants";

enum PreviousType {
  empresa = "empresa",
  pessoa = "pessoa"
}

export const getRelationshipLabels = (vinculoValue: string, index?: number, usePrefix: boolean = false, previousType?: string): string => {
  if (!vinculoValue || typeof vinculoValue !== "string") return String(vinculoValue);

  const normalizedInput = vinculoValue.toLowerCase().trim();

  const matchKey = Object.keys(RELACOES_VINCULOS.vinculos).find(key => {
    const normalizedKey = key.toLowerCase();
    return normalizedKey === normalizedInput ||
      normalizedKey.includes(normalizedInput) ||
      normalizedInput.includes(normalizedKey);
  });

  if (matchKey) {
    let relationshipValue: string;

    if ( previousType === PreviousType.empresa && RELACOES_VINCULOS.vinculos_from_empresa[matchKey as keyof typeof RELACOES_VINCULOS.vinculos_from_empresa]) {
      relationshipValue = RELACOES_VINCULOS.vinculos_from_empresa[matchKey as keyof typeof RELACOES_VINCULOS.vinculos_from_empresa];
    } else if (previousType === PreviousType.pessoa && RELACOES_VINCULOS.vinculos_from_pessoa[matchKey as keyof typeof RELACOES_VINCULOS.vinculos_from_pessoa]) {
      relationshipValue = RELACOES_VINCULOS.vinculos_from_pessoa[matchKey as keyof typeof RELACOES_VINCULOS.vinculos_from_pessoa];
    } else {
      relationshipValue = RELACOES_VINCULOS.vinculos[matchKey as keyof typeof RELACOES_VINCULOS.vinculos];
    }

    if (usePrefix && index !== undefined) {
      const prefix = RELACOES_VINCULOS.prefixo_semantico[String(index) as keyof typeof RELACOES_VINCULOS.prefixo_semantico];
      return prefix ? `${prefix} ${relationshipValue}` : relationshipValue;
    }

    return relationshipValue;
  }

  return vinculoValue;
}