import { <PERSON><PERSON>erRouter } from "react-router";
import Router from "~/components/router/Router";
import ReactQueryWrapper from "./components/ReactQueryWrapper";
import { ThemeProvider } from "~/context/themeContext";
import { AuthEventHandler } from "./components/AuthEventHandler";
import { SecretKeyInitializer } from "./components/SecretKeyInitializer";
import { Toaster } from "~/components/ui/sonner";
import { TooltipProvider } from "./components/ui/tooltip";
import "./index.css";

const App = () => {
  return (
    <ReactQueryWrapper>
      <ThemeProvider>
        <TooltipProvider>
          <BrowserRouter>
            <AuthEventHandler />
            {/* <SecretKeyInitializer /> */}
            <Router />
          </BrowserRouter>
          <Toaster />
        </TooltipProvider>
      </ThemeProvider>
    </ReactQueryWrapper>
  );
};

export default App;
