services:
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    restart: always
    command:
      - --web.enable-remote-write-receiver
      - --config.file=/etc/prometheus/prometheus.yml
    ports:
      - "9090:9090" # Expose Prometheus on port 9090
    volumes:
      - ./infrastructure/prometheus.yml:/etc/prometheus/prometheus.yml # Mount Prometheus configuration
    networks:
      - monitoring

  # grafana:
  #   image: grafana/grafana:11.0.0
  #   ports:
  #     - "3000:3000"
  #   container_name: grafana
  #   restart: always
  #   volumes:
  #     - grafana-storage:/var/lib/grafana # Persist Grafana data
  #   environment:
  #     - GF_PATHS_PROVISIONING=/etc/grafana/provisioning
  #     - GF_AUTH_ANONYMOUS_ENABLED=true
  #     - GF_AUTH_ANONYMOUS_ORG_ROLE=Admin
  #   networks:
  #     - monitoring

  # tempo:
  #   image: grafana/tempo:2.6.0
  #   ports:
  #     - "3200:3200"
  #     - "4317:4317"
  #     - "4318:4318"
  #     - "9411:9411"
  #     - "55680:55680"
  #     - "55681:55681"
  #     - "14250:14250"
  #   command: [ "-config.file=/etc/tempo.yaml" ]
  #   volumes:
  #     - "./infrastructure/tempo.yaml:/etc/tempo.yaml"
  #   networks:
  #     - monitoring

  # mimir:
  #   image: grafana/mimir:2.13.0
  #   command: [ "-ingester.native-histograms-ingestion-enabled=true", "-config.file=/etc/mimir.yaml" ]
  #   ports:
  #     - "9009:9009"
  #   volumes:
  #     - "./infrastructure/mimir.yaml:/etc/mimir.yaml"
  #   networks:
  #     - monitoring

  # loki:
  #   image: grafana/loki:latest
  #   container_name: loki
  #   restart: always
  #   ports:
  #     - "3100:3100" # Default Loki HTTP port
  #   command: [ "-config.file=/etc/loki/local-config.yaml" ]
  #   networks:
  #     - monitoring
  #   volumes:
  #     - "./infrastructure/loki.yaml:/etc/loki/local-config.yaml"

  # frontend:
  #   build:
  #     context: ./apps/frontend
  #     dockerfile: ./Dockerfile # Reference your app's Dockerfile
  #     args:
  #       - VITE_API_URL=http://bff:3001 # Override for Docker
  #   container_name: frontend
  #   restart: always
  #   ports:
  #     - "8080:8080" # Expose frontend application on port 8080
  #   networks:
  #     - monitoring
  #   environment:
  #     - NODE_ENV=production
  #     - VITE_REPORTS_API_URL=${VITE_REPORTS_API_URL:-http://localhost:8000}
  #     - VITE_PDF_SERVICE_URL=${VITE_PDF_SERVICE_URL:-http://localhost:3002}
  #     - FARO_TRACE_EXPORTER=http://grafana-alloy:12345/v1/traces # Point Faro to Alloy
  #     - FARO_METRICS_EXPORTER=http://grafana-alloy:12345/v1/metrics
  #     - FARO_LOG_EXPORTER=http://loki:3100/loki/api/v1/push # Send observability logs to Loki
  #     - VITE_API_URL=${VITE_API_URL:-http://bff:3001} # Allow runtime override


  # nginx-metrics:
  #   image: nginx/nginx-prometheus-exporter:latest
  #   ports:
  #     - "9113:9113"
  #   command:
  #     - -nginx.scrape-uri
  #     - http://frontend:8080/stub_status
  #   networks:
  #     - monitoring

  # grafana-alloy:
  #   image: grafana/alloy:latest
  #   container_name: grafana-alloy
  #   restart: always
  #   volumes:
  #     - ./infrastructure/config.alloy:/etc/alloy/config.alloy
  #     - ./logs:/tmp/app-logs/
  #   ports:
  #     - "12345:12345"
  #     - "12347:12347"
  #   networks:
  #     - monitoring
  #   command: [ "run", "--server.http.listen-addr=0.0.0.0:12345", "--storage.path=/var/lib/alloy/data", "--stability.level=public-preview", "/etc/alloy/config.alloy" ]
  #   depends_on:
  #     - loki

  # kafka-exporter:
  #   image: danielqsj/kafka-exporter:latest
  #   command:
  #     - --kafka.server=kafka:9092
  #     - --web.listen-address=:9308
  #   ports:
  #     - "9308:9308"
  #   networks:
  #     - monitoring
  #     - mystack-net
  #   restart: always

# volumes:
#   grafana-storage:
#     # Retain Grafana dashboards and configurations

networks:
  monitoring:
    # Define a network for communication between containers
    driver: overlay
    attachable: true
