import { Button } from '@snap/design-system'
import React, { useMemo } from 'react'
import SemanticAnalisysTab from './SemanticAnalisysTab';
import { useVisibleReportSections, useReportMode, useIsSaving, useIsTrashEnabled, useReportSections } from '../../context/ReportContext';
import { filterValidSections } from '../../helpers';

const RenderRelacoes = () => {
  const [activeTab, setActiveTab] = React.useState('report')
    const sections = useVisibleReportSections();

  const handleTabClick = (tab: string) => {
    setActiveTab(tab);
  };

  // const fetchMock = async () => {
  //   const response = await fetch('/mock_retorno_processado_relacoes.json');
  //   const data = await response.json();
  //   console.log(data[0].data);
  //   setData(data[0].data);
  // }

  // React.useEffect(() => {
  //   fetchMock();
  // }, []);

  return (
    <div className='flex flex-col gap-4'>
      <div className="flex items-center">
        <Button
          className={`uppercase w-full !rounded-none ${activeTab === "report" && "!bg-foreground !text-background"}`}
          onClick={(e) => handleTabClick("report")}
          data-testid="button-create-report"
        >
          análise semântica
        </Button>
        <Button
          className={`uppercase w-full !rounded-none ${activeTab === "folder" && "!bg-foreground !text-background"}`}
          onClick={(e) => handleTabClick("folder")}
          data-testid="button-create-folder"
        >
          fluxo de relacionamento
        </Button>
      </div>

      <div className="flex-1 md:flex-3/4 max-h-[calc(100vh-200px)] overflow-y-auto overflow-x-hidden [scrollbar-gutter:stable] bg-background" id="report-content">
        {activeTab === "report" && (
          <div>
            <SemanticAnalisysTab data={sections[0].data || []} />
          </div>
        )}
        {activeTab === "folder" && (
          <div>
            <h1>Fluxo de Relacionamento</h1>
          </div>
        )}
      </div>
    </div>
  )
}

export default RenderRelacoes