import { Button, <PERSON> } from "@snap/design-system";
import { Check, Folder, X } from "lucide-react";
import { FolderTreeExplorer } from "~/components/FolderTreeExplorer";
import { useFolderCRUD } from "~/hooks/useFolderCRUD";
import { useSelectedFolder, useFolderListActions } from "~/store/folderListStore";
import { useDialogActions } from "~/store/dialogStore";
import { AiOutlineLoading3Quarters } from "react-icons/ai";
import { FaFolder } from "react-icons/fa";
import { filterHierarchicalFolders } from "~/helpers/folderTree.helper";

interface MoveFolderDialogContentProps {
  folderId: string;
  currentFolderId?: string | null;
}

export function MoveFolderDialogContent({ currentFolderId, folderId }: MoveFolderDialogContentProps) {
  const { setSelectedFolder, clearSelectedFolder } = useFolderListActions();
  const selectedFolder = useSelectedFolder();
  const { useHierarchicalFoldersQuery, moveFolderToFolderMutation } = useFolderCRUD();
  const { data: hierarchicalFolders, isLoading, error, refetch } = useHierarchicalFoldersQuery();

  // filtrar pasta atual
  const filteredFolders = hierarchicalFolders && Array.isArray(hierarchicalFolders) && folderId
    ? filterHierarchicalFolders(hierarchicalFolders, folderId)
    : hierarchicalFolders || [];

  const handleFolderSelect = (folderId: string | null) => {
    setSelectedFolder(folderId === null ? "root" : folderId);
  };

  const renderActionButton = (targetFolderId: string) => {

    const handleMoveToFolder = () => {
      const destinationFolderId = targetFolderId === "root" ? null : targetFolderId;

      moveFolderToFolderMutation.mutate({
        folderId,
        sourceFolderId: currentFolderId,
        destinationFolderId,
      }, {
        onSuccess: () => {
          clearSelectedFolder();
        }
      });
    };

    return (
      <Button
        className="ml-2 uppercase !bg-foreground !text-background !font-bold disabled:opacity-60 hover:opacity-80"
        icon={<Check size={16} />}
        iconPosition="right"
        onClick={handleMoveToFolder}
        disabled={moveFolderToFolderMutation.isPending}
        loading={moveFolderToFolderMutation.isPending}
        size="sm"
      >
        Mover para pasta
      </Button>
    );
  };

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center py-8 space-y-4">
        <AiOutlineLoading3Quarters className="size-8 animate-spin" />
        <Text variant="body-md">Carregando pastas...</Text>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center py-8 space-y-4">
        <X className="size-8 text-primary" />
        <Text variant="body-md" className="text-center">
          Erro ao carregar pastas: {error.message}
        </Text>
        <Button
          size="sm"
          onClick={() => refetch()}
          className="uppercase"
        >
          Tentar novamente
        </Button>
      </div>
    );
  }

  if (filteredFolders.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-8 space-y-4">
        <FaFolder className="size-8 text-muted-foreground" />
        <Text variant="body-md" className="text-center">
          Nenhuma pasta disponível para mover a pasta
        </Text>
      </div>
    );
  }

  return (
    <div className="space-y-4 min-h-[300px]">
      <Text variant="body-lg" className="py-2">
        Selecione a <span className="font-bold">pasta de destino:</span>
      </Text>

      <FolderTreeExplorer
        folders={filteredFolders}
        selectedFolderId={selectedFolder === "root" ? null : selectedFolder}
        onFolderSelect={handleFolderSelect}
        excludeFolderId={folderId}
        showRootOption={true}
        rootOptionLabel="Início - Dashboard"
        className="min-h-[300px]"
        renderActionButton={renderActionButton}
        currentItemParentFolderId={currentFolderId}
      />
    </div>
  );
}

export function MoveFolderDialogFooter() {
  const { closeDialog } = useDialogActions();
  const { clearSelectedFolder } = useFolderListActions();
  const { moveFolderToFolderMutation } = useFolderCRUD();

  const handleCancel = () => {
    clearSelectedFolder();
    closeDialog();
  };

  return (
    <div className="flex gap-3">
      <Button
        className="uppercase !bg-transparent"
        onClick={handleCancel}
        disabled={moveFolderToFolderMutation.isPending}
      >
        Cancelar
      </Button>
    </div>
  );
}

export const MoveFolderDialog = {
  Content: MoveFolderDialogContent,
  Footer: MoveFolderDialogFooter,
};