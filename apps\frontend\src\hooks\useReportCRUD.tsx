import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  fetchReportList,
  fetchReportById,
  createNewReport,
  addNewReport,
  deleteReport,
  generatePDF,
  renameReport,
} from "~/services/gateways/report.gateway";
import { useEncryption } from "~/hooks/useEncryption";

import { useUserIsVerified } from "~/store/userStore";
import type {
  EncryptedPayload,
  ReportData,
  ReportPDFProps,
} from "~/types/global";
import { FolderData } from "root/domain/entities/folder.model";
import { toast } from "sonner";
import { useDialogActions } from "~/store/dialogStore";
import { useNewPendingReportsActions } from "~/store/newReportStatusStore";
import {
  decryptReportPayload,
  encryptReportPayload,
} from "~/helpers/encryption.helper";
import { REPORT_CONSTANTS, tanstackQueryConfig } from "~/helpers/constants";
import { useReportListLoadCount, useReportListPage, useReportListActions, useReportListSearchFilter } from "~/store/reportListStore";

import { createErrorHandler } from "~/helpers/errorHandling.helper";
import { decryptListItem } from "~/helpers/item-decryption.helper";

export const useReportCRUD = (folderId?: string | null, enableQuery: boolean = true) => {
  const { closeDialog } = useDialogActions();
  const queryClient = useQueryClient();
  const { decryptData, encryptData, encryptNgrams } = useEncryption();

  const { clearPendingList } = useNewPendingReportsActions();
  const isVerified = useUserIsVerified();

  const pageNumber = useReportListPage();
  const limitNumber = useReportListLoadCount();
  const searchFilter = useReportListSearchFilter();
  const { setReportList, incrementReportList, resetPagination, setLoadMore, clearSearchFilter } = useReportListActions();

  const reportListQuery = useQuery({
    queryKey: ["reports", "list", folderId, isVerified],
    queryFn: async ({ meta }) => {
      try {
        console.log("Query running with verified user");
        clearPendingList();

        let hmacFilters
        // Se houver valor no campo de busca, transformar em n-grams e criptografar para usar de filtro
        if (searchFilter) {
          console.log("reportListQuery searchFilter", searchFilter);
          const ngrams = await encryptNgrams({
            searched_value: searchFilter,
          });
          hmacFilters = ngrams.searched_value
          //clearSearchFilter()
        }

        const reportsListData = await fetchReportList({
          page: pageNumber,
          limit: limitNumber,
          hmacFilters,
          folderId: folderId
        });

        const decryptedItemsList = await Promise.all(
          reportsListData.map(async (item, index) => {
            try {
              return await decryptListItem(item, decryptData);
            } catch (error) {
              console.error(`Error decrypting item #${index + 1}:`, error);
              throw error;
            }
          })
        );

        const finalList = decryptedItemsList;

        //@ts-ignore
        pageNumber === 1 ? setReportList(finalList) : incrementReportList(finalList)
        const itemCount = finalList.length
        const hasMore = Boolean(itemCount === limitNumber)
        setLoadMore(hasMore)
        console.log("decrypted items list", finalList);

        // TODO - remover caso não esteja agregando NADA - teste para evitar mensagem de refetch sempre que chamar a lista
        const isScheduledRefetch = meta?.fetchReason === "refetch";
        if (isScheduledRefetch) {
          sessionStorage.removeItem("reports_list_toast_shown");
        }

        return {
          data: finalList as (ReportData | FolderData)[],
          isScheduledRefetch: isScheduledRefetch,
        };
      } catch (err: any) {
        console.error("Error fetching report list:", err);
        toast.error("Erro", {
          description: "Ocorreu um erro ao tentar atualizar a lista.",
        });
        return {
          data: [],
          isScheduledRefetch: false,
        }; // TODO - organizar errors (retornos e componentes)
      }
    },
    ...tanstackQueryConfig,
    staleTime: Infinity,
    refetchInterval: 1000 * 60 * 60, // refetch em 1h para o caso do usuário estar idle e o access token expirado
    enabled: enableQuery && isVerified, // Only run when user is verified
    meta: {
      fetchReason: "initial",
    },
  });

  const reportDetailsQuery = (id: string) =>
    useQuery<ReportData, Error>({
      queryKey: ["reports", "details", id, isVerified],
      queryFn: async () => {
        const response = await fetchReportById(id);
        const decrypted = await decryptReportPayload(response as ReportData, decryptData);
        console.log("[useReportCRUD] reportDetailsQuery decrypted", decrypted);
        return decrypted as ReportData;

        // const repoonse = await fetch("/mock_retorno_processado_relacoes.json");
        // const data = await repoonse.json();
        // console.log("[useReportCRUD] reportDetailsQuery data", data);
        // return data as ReportData;
      },
      staleTime: Infinity,
      enabled: !!id && isVerified, // Only run when user is verified
    });

  const newReportMutation = useMutation({
    mutationFn: (payload: any) => createNewReport(payload),
    onSuccess: async (data, variables) => {
      await invalidateCurrentFolderNoFilters(variables.parent_folder_id || null);
      closeDialog();

      toast.success("Gerando novo relatório...");
      window.dispatchEvent(new Event("snap:report-created"));
    },

    onError: createErrorHandler("Ocorreu um erro ao tentar criar um novo relatório", "Erro ao criar relatório"),
  });

  const addNewReportMutation = useMutation({
    mutationFn: async (report: any) => {
      try {
        const { user_reports_id } = report;
        if (user_reports_id) {
          const ngrams = await encryptNgrams({
            report_name: report.report_name,
            report_search_args: report.report_search_args,
            subject_name: report.subject_name,
            subject_mother_name: report.subject_mother_name,
          });

          /* não enviar mais created_at e modified_at */
          const { created_at, modified_at, ...newReport } = report
          console.log("[useReportCRUD] addNewReportMutation newReport", newReport);

          const modifiedReports = {
            ...newReport,
            [REPORT_CONSTANTS.new_report.report_status]: REPORT_CONSTANTS.status.completed,

          };

          const encryptedReport = await encryptReportPayload(
            modifiedReports,
            encryptData
          );

          const payload = {
            ...encryptedReport,
            [REPORT_CONSTANTS.new_report.hmac]: { [REPORT_CONSTANTS.new_report.ngrams]: ngrams } // formato exigido pelo backend
          };

          console.log("[useReportCRUD] addNewReportMutation payload", payload);
          return addNewReport(payload, user_reports_id);
        }
      } catch (error) {
        console.error("Error encrypting report:", error);
        throw new Error("Failed to encrypt report data");
      }
    },
  });

  const deleteReportMutation = useMutation({
    mutationFn: async (reportId: string) => {
      try {
        await deleteReport(reportId);
      } catch (error) {
        console.error("Error deleting report:", error);
        throw error;
      }
    },
    onSuccess: async (data: any) => {
      invalidateCurrentFolder(folderId || null);
      closeDialog();

      toast.success(`Relatório deletado com sucesso!`);
    },
    onError: (error: Error) => {
      createErrorHandler(
        "Ocorreu um erro ao tentar deletar o relatório",
        "Erro ao deletar relatório"
      )(error);
    },
  });

  const invalidateReports = async () => {
    await queryClient.invalidateQueries({
      queryKey: ["reports", "list"],
      exact: false,
    });
  };

  // TODO - refatorar para não precisar usar diferentes funções para invalidar a lista de relatórios
  const invalidateToInitialPage = async () => {
    await resetPagination();
    await queryClient.invalidateQueries({
      queryKey: ["reports", "list"],
      exact: false,
    });
  };

  const invalidateReportDetails = async (id: string) => {
    await queryClient.invalidateQueries({
      queryKey: ["reports", "details", id],
      exact: true,
    });
  };

  const setReportDetailsToStale = async (id: string) => {
    await queryClient.invalidateQueries({
      queryKey: ["reports", "details", id],
      exact: true,
      refetchType: 'none', // Marca como stale mas não faz refetch imediatamente
    });
  };

  // Alternativa usando resetQueries - remove os dados do cache forçando refetch na próxima renderização
  const resetReportListQuery = async () => {
    await queryClient.resetQueries({
      queryKey: ["reports", "list"],
      exact: true,
    });
  };

  const invalidateCurrentFolder = (targetFolderId: string | null) => {
    queryClient.invalidateQueries({
      queryKey: ["reports", "list", targetFolderId],
      exact: true,
    });
  };

  const invalidateAllReports = () => {
    queryClient.invalidateQueries({
      queryKey: ["reports", "list"],
      exact: false,
    });
  };
  const invalidateCurrentFolderNoFilters = async (targetFolderId: string | null) => {
    await resetPagination();
    await clearSearchFilter();
    await queryClient.invalidateQueries({
      queryKey: ["reports", "list", targetFolderId],
      exact: true,
    });
  };

  const generatePDFMutation = useMutation({
    mutationFn: async (props: ReportPDFProps) => {
      try {
        const url = await generatePDF(props);
        return url;
      } catch (error) {
        console.error("Error generating PDF:", error);
        throw error;
      }
    },
    onError: (error: Error) => {
      console.error("Error generating PDF:", error);
    },
  });

  const renameReportMutation = useMutation({
    mutationFn: async ({ report_id, report_name }: { report_id: string; report_name: string }) => {
      try {
        // HMAC
        const hmacResult = await encryptNgrams({
          report_name: report_name,
        });
        // CRIPTOGRAFIA
        const encryptedReportName = await encryptData(report_name || "");
        await renameReport({
          [REPORT_CONSTANTS.new_report.report_id]: report_id,
          [REPORT_CONSTANTS.new_report.report_name]: encryptedReportName.data as EncryptedPayload,
          [REPORT_CONSTANTS.new_report.hmac]: { [REPORT_CONSTANTS.new_report.ngrams]: hmacResult },
        });
      } catch (error) {
        console.error("Error renaming report:", error);
        throw error;
      }
    },
    onSuccess: async (data: any) => {
      invalidateCurrentFolderNoFilters(folderId || null);
      closeDialog();

      toast.success(`Relatório renomeado com sucesso!`);
    },
    onError: (error: Error) => {
      createErrorHandler(
        "Ocorreu um erro ao tentar renomear o relatório",
        "Erro ao renomear relatório"
      )(error);
    },
  });

  return {
    reportListQuery,
    reportDetailsQuery,
    invalidateReports,
    invalidateToInitialPage,
    invalidateReportDetails,
    setReportDetailsToStale,
    resetReportListQuery,
    invalidateCurrentFolder,
    invalidateAllReports,
    invalidateCurrentFolderNoFilters,
    newReportMutation,
    addNewReportMutation,
    deleteReportMutation,
    generatePDFMutation,
    renameReportMutation
  };
};