import { useCallback } from "react";
import { useUserActions, useUserSalt } from "~/store/userStore";
import { encryptNgramsDeterministic, deriveEncryptionKeyArgon2 } from "~/helpers/encryption.helper";
import { encryptionWorker } from "~/services/workers/encryptionWorker.service";
import { getStoredEncryptionKey, base64ToArrayBuffer, useSecretKeyActions } from "~/store/secretKeyStore";
import { EncryptedPayload } from "~/types/global";

export interface CryptoResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

export const useEncryption = () => {
  const { setIsVerified } = useUserActions();
  const { clearStoredKey } = useSecretKeyActions();
  const userSalt = useUserSalt();

  const deriveKey = useCallback(async (): Promise<ArrayBuffer> => {
    // First, try to get from storage
    const storedKey = getStoredEncryptionKey();
    if (storedKey) {
      return base64ToArrayBuffer(storedKey);
    }

    // If no stored key, check for one-time key
    const oneTimeKey = (window as any).__oneTimeSecretKey;
    if (oneTimeKey && userSalt) {
      try {
        const key = await deriveEncryptionKeyArgon2(oneTimeKey, userSalt);
        const rawKey = await crypto.subtle.exportKey("raw", key);

        // Clear the one-time key after use for security
        delete (window as any).__oneTimeSecretKey;

        return rawKey;
      } catch (error) {
        console.error("Error deriving one-time key:", error);
        delete (window as any).__oneTimeSecretKey;
      }
    }

    // No valid key found - trigger re-authentication
    setIsVerified(false);
    throw new Error("No valid stored key - user needs to re-authenticate");
  }, [setIsVerified, userSalt]);

  const encryptData = useCallback(
    async (data: any): Promise<CryptoResult<EncryptedPayload>> => {
      try {
        const rawKey = await deriveKey();
        const encryptedData = await encryptionWorker.encrypt(data, rawKey);
        return {
          success: true,
          data: encryptedData as EncryptedPayload,
        };
      } catch (error) {
        console.error("Encryption error:", error);

        // If key-related error, clear stored key and force re-authentication
        if (error instanceof Error && error.message.includes("stored key")) {
          clearStoredKey();
          setIsVerified(false);
        }

        return {
          success: false,
          error:
            error instanceof Error ? error.message : "Unknown encryption error",
        };
      }
    },
    [deriveKey, clearStoredKey, setIsVerified]
  );

  const decryptData = useCallback(
    async (encryptedData: EncryptedPayload): Promise<CryptoResult<any>> => {
      try {
        const rawKey = await deriveKey();
        const decryptedData = await encryptionWorker.decrypt(
          encryptedData,
          rawKey
        );
        return {
          success: true,
          data: decryptedData,
        };
      } catch (error) {
        console.error("Decryption error:", error);

        // If key-related error, clear stored key and force re-authentication
        if (error instanceof Error && error.message.includes("stored key")) {
          clearStoredKey();
          setIsVerified(false);
        }

        return {
          success: false,
          error:
            error instanceof Error ? error.message : "Unknown decryption error",
        };
      }
    },
    [deriveKey, clearStoredKey, setIsVerified]
  );


  /**
   * Criptografa os n-grams de um relatório.
   * @param reportObj Objeto contendo os campos do relatório a serem pesquisáveis.
   * @returns Um objeto contendo os n-grams criptografados.
   */
  const encryptNgrams = useCallback(
    async (reportObj: Record<string, any>): Promise<Record<string, string[]>> => {
      const rawKey = await deriveKey();
      return encryptNgramsDeterministic(rawKey, reportObj);
    },
    [deriveKey]
  );

  return {
    encryptData,
    decryptData,
    encryptNgrams,
    salt: userSalt,
  };
};
