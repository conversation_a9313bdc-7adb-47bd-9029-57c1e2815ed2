import { useCallback } from "react";
import { useUserActions } from "~/store/userStore";
import { ensureValidStoredKey, useSecretKeyActions } from "~/store/secretKeyStore";

/**
 * Hook that provides utilities for validating stored secret keys throughout the app.
 * Use this hook in action buttons and components that require encryption/decryption.
 */
export const useSecretKeyValidation = () => {
  const { setIsVerified } = useUserActions();
  const { clearStoredKey } = useSecretKeyActions();

  /**
   * Validates if there's a valid stored secret key.
   * If no valid key is found, automatically triggers re-authentication.
   * 
   * @returns boolean - true if valid key exists, false otherwise
   */
  const validateStoredKey = useCallback((): boolean => {
    const isValid = ensureValidStoredKey();
    
    if (!isValid) {
      // No valid key found - trigger re-authentication
      setIsVerified(false);
      clearStoredKey();
    }
    
    return isValid;
  }, [setIsVerified, clearStoredKey]);

  /**
   * Use this function before any action that requires encryption/decryption.
   * Returns true if the action can proceed, false if user needs to re-authenticate.
   * 
   * Example usage:
   * ```
   * const { requireValidKey } = useSecretKeyValidation();
   * 
   * const handleEncryptedAction = () => {
   *   if (!requireValidKey()) {
   *     // User will be prompted to re-authenticate
   *     return;
   *   }
   *   // Proceed with encrypted action
   *   performEncryptedOperation();
   * };
   * ```
   */
  const requireValidKey = useCallback((): boolean => {
    return validateStoredKey();
  }, [validateStoredKey]);

  return {
    validateStoredKey,
    requireValidKey,
  };
};
