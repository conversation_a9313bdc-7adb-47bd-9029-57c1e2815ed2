import { useCallback, createElement } from "react";
import { useUserActions, useUserData } from "~/store/userStore";
import { getStoredEncryptionKey, useSecretKeyActions, type StorageDuration } from "~/store/secretKeyStore";
import { useModalControl } from "@snap/design-system";
import { SecretKeyDialog } from "~/containers/report/SecretKeyDialog";
import { KeyRound } from "lucide-react";

/**
 * Hook that provides utilities for validating stored secret keys and handling re-authentication.
 * This hook manages the secret key dialog at the action level.
 */
export const useSecretKeyValidation = () => {
  const { setIsVerified } = useUserActions();
  const { clearStoredKey } = useSecretKeyActions();
  const userData = useUserData();
  const { open } = useModalControl();

  /**
   * Checks if stored key is valid and not expired
   */
  const isStoredKeyValid = useCallback((): boolean => {
    const storedKey = getStoredEncryptionKey();
    return !!storedKey;
  }, []);

  /**
   * Opens the secret key dialog for re-authentication
   */
  const openSecretKeyDialog = useCallback((): Promise<boolean> => {
    return new Promise((resolve) => {
      const handleDialogClose = () => {
        // Check if key is now valid after dialog closes
        const isValid = isStoredKeyValid();
        if (isValid) {
          setIsVerified(true);
        }
        resolve(isValid);
      };

      open({
        modal: () => ({
          title: userData?.verifier ? "INSERIR SENHA" : "CRIAR SENHA",
          icon: createElement(KeyRound),
          content: createElement(SecretKeyDialog.Content),
          footer: createElement(SecretKeyDialog.Footer, { onOpen: () => { } }),
        }),
        config: {
          content: {
            className: "max-w-xl",
          },
          // onClose: handleDialogClose,
        },
      });
    });
  }, [userData, open, setIsVerified, isStoredKeyValid]);

  /**
   * Validates stored key and prompts for re-authentication if needed.
   * This is the main function to use before any encrypted action.
   *
   * @returns Promise<boolean> - true if valid key exists or was provided, false if user cancelled
   */
  const ensureValidKey = useCallback(async (): Promise<boolean> => {
    // Check if key is valid
    if (isStoredKeyValid()) {
      return true;
    }

    // Clear expired/invalid key
    clearStoredKey();
    setIsVerified(false);

    // Prompt user for re-authentication
    return await openSecretKeyDialog();
  }, [isStoredKeyValid, clearStoredKey, setIsVerified, openSecretKeyDialog]);

  return {
    ensureValidKey,
    isStoredKeyValid,
  };
};
