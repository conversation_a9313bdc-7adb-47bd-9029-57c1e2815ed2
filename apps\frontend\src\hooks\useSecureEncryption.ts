import { useCallback } from "react";
import { useEncryption, type CryptoResult } from "~/hooks/useEncryption";
import { useSecretKeyValidation } from "~/hooks/useSecretKeyValidation";
import { type EncryptedPayload } from "~/types/global";

/**
 * Hook that provides encryption operations with automatic secret key validation.
 * This hook ensures that the user is prompted for their secret key when needed,
 * and handles both stored keys and one-time usage scenarios.
 */
export const useSecureEncryption = () => {
  const { encryptData: baseEncryptData, decryptData: baseDecryptData, encryptNgrams: baseEncryptNgrams } = useEncryption();
  const { ensureValidKey } = useSecretKeyValidation();

  /**
   * Encrypts data with automatic key validation.
   * If no valid key is stored or it's expired, prompts user for re-authentication.
   * 
   * @param data - Data to encrypt
   * @returns Promise<CryptoResult<EncryptedPayload> | null> - null if user cancelled authentication
   */
  const encryptData = useCallback(
    async (data: any): Promise<CryptoResult<EncryptedPayload> | null> => {
      // Ensure we have a valid key before proceeding
      const hasValidKey = await ensureValidKey();
      if (!hasValidKey) {
        return null; // User cancelled authentication
      }

      // Proceed with encryption
      return await baseEncryptData(data);
    },
    [baseEncryptData, ensureValidKey]
  );

  /**
   * Decrypts data with automatic key validation.
   * If no valid key is stored or it's expired, prompts user for re-authentication.
   * 
   * @param encryptedData - Data to decrypt
   * @returns Promise<CryptoResult<any> | null> - null if user cancelled authentication
   */
  const decryptData = useCallback(
    async (encryptedData: EncryptedPayload): Promise<CryptoResult<any> | null> => {
      // Ensure we have a valid key before proceeding
      const hasValidKey = await ensureValidKey();
      if (!hasValidKey) {
        return null; // User cancelled authentication
      }

      // Proceed with decryption
      return await baseDecryptData(encryptedData);
    },
    [baseDecryptData, ensureValidKey]
  );

  /**
   * Encrypts n-grams with automatic key validation.
   * If no valid key is stored or it's expired, prompts user for re-authentication.
   * 
   * @param reportObj - Object containing report fields to be searchable
   * @returns Promise<Record<string, string[]> | null> - null if user cancelled authentication
   */
  const encryptNgrams = useCallback(
    async (reportObj: Record<string, any>): Promise<Record<string, string[]> | null> => {
      // Ensure we have a valid key before proceeding
      const hasValidKey = await ensureValidKey();
      if (!hasValidKey) {
        return null; // User cancelled authentication
      }

      // Proceed with n-grams encryption
      return await baseEncryptNgrams(reportObj);
    },
    [baseEncryptNgrams, ensureValidKey]
  );

  /**
   * Wrapper function for any action that requires encryption.
   * Handles key validation and provides a clean API for action functions.
   * 
   * @param action - Function that performs the encrypted operation
   * @returns Promise<T | null> - null if user cancelled authentication
   */
  const withSecureKey = useCallback(
    async <T>(action: () => Promise<T>): Promise<T | null> => {
      // Ensure we have a valid key before proceeding
      const hasValidKey = await ensureValidKey();
      if (!hasValidKey) {
        return null; // User cancelled authentication
      }

      // Execute the action
      return await action();
    },
    [ensureValidKey]
  );

  return {
    encryptData,
    decryptData,
    encryptNgrams,
    withSecureKey,
  };
};
