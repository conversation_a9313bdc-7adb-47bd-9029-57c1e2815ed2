import { useCallback, useEffect, useRef } from "react";
import { toast } from "sonner";
import { REPORT_CONSTANTS } from "~/helpers/constants";
import { useReportCRUD } from "~/hooks/useReportCRUD";
import { useUserData } from "~/store/userStore";
import { useSecretKeyValidation } from "~/hooks/useSecretKeyValidation";
import { createErrorHandler } from "~/helpers/errorHandling.helper";

export function PendingReportSockets() {
  const { addNewReportMutation, invalidateCurrentFolderNoFilters } = useReportCRUD(null, false);
  const userData = useUserData();
  const { isStoredKeyValid } = useSecretKeyValidation();
  const wsRef = useRef<WebSocket | null>(null);

  const connect = useCallback(() => {
    if (!userData?.user_id || !isStoredKeyValid()) return;
    wsRef.current?.close();

    const userId = userData.user_id;
    const url = import.meta.env.DEV
      ? `ws://localhost:8000/reports/ws/snap-status/${userId}`
      : `wss://${window.location.host}/reports/ws/snap-status/${userId}`;
    const ws = new WebSocket(url);
    wsRef.current = ws;

    ws.onopen = async () => {
      console.log(`[PendingReportSockets] - Conexão WebSocket aberta para ${url}`);
    };

    ws.onmessage = (evt) => {
      console.log("[PendingReportSockets] - Mensagem recebida via WebSocket:", evt)
      console.log("[PendingReportSockets] - Mensagem recebida via WebSocket:", evt.data)
      let payload;
      try {
        payload = JSON.parse(evt.data);
      } catch (error) {
        console.log(`[PendingReportSockets] - Erro ao parsear mensagem WebSocket: ${error}`);
        return;
      }
      const status = payload[REPORT_CONSTANTS.report_status_message.status];
      const { completed, error } = REPORT_CONSTANTS.status;
      console.log("[PendingReportSockets] - Status do relatório:", status);
      if (status === error) {
        console.log("[PendingReportSockets] - Relatório com erro:", payload);
        const currentFolderId = window.location.pathname.startsWith('/pasta/')
          ? window.location.pathname.split('/pasta/')[1]
          : null;
        invalidateCurrentFolderNoFilters(currentFolderId);
        toast.error("Erro ao gerar relatório", {
          description: "Ocorreu um erro ao tentar gerar o relatório. Tente novamente.",
        });
      }

      if (status === completed) {
        const newReport = payload.result?.data;
        if (newReport) {
          addNewReportMutation.mutate(newReport, {
            onSuccess: () => {
              const currentFolderId = window.location.pathname.startsWith('/pasta/')
                ? window.location.pathname.split('/pasta/')[1]
                : null;
              invalidateCurrentFolderNoFilters(currentFolderId);
              toast.success("Relatório gerado com sucesso!", {
                description: "Seu relatório está pronto para ser acessado.",
              });
            },
            onError: createErrorHandler("Erro ao tentar salvar o novo relatório", "Erro ao salvar"),
          });
        }
      }
    };
    ws.onerror = (e) => {
      console.error("[PendingReportSockets] Erro na conexão WebSocket:", e)
    };
    ws.onclose = (e) => console.warn("[PendingReportSockets] Conexão WebSocket fechada:", e);
  }, [userData, isStoredKeyValid]);

  // Conectar ao renderizar e desconectar ao desmontar
  useEffect(() => {
    connect();
    return () => wsRef.current?.close();
  }, [connect]);

  //evento global para reconectar o websocket após a criação de um novo relatório
  useEffect(() => {
    const handler = () => {
      const ws = wsRef.current;
      if (!ws || ws.readyState !== WebSocket.OPEN) {
        console.log("[PendingReportSockets] reconnecting after report creation event");
        connect();
      }
    };
    window.addEventListener('snap:report-created', handler);
    return () => window.removeEventListener('snap:report-created', handler);
  }, [connect]);

  return null;
}