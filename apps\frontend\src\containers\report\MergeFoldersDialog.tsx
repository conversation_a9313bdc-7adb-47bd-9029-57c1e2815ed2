import { Button, <PERSON> } from "@snap/design-system";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Check, Folder, X } from "lucide-react";
import { FolderTreeExplorer } from "~/components/FolderTreeExplorer";
import { useFolderCRUD } from "~/hooks/useFolderCRUD";
import { useSelectedFolder, useFolderListActions } from "~/store/folderListStore";
import { useDialogActions } from "~/store/dialogStore";
import { AiOutlineLoading3Quarters } from "react-icons/ai";
import { FaFolder } from "react-icons/fa";
import { filterHierarchicalFolders } from "~/helpers/folderTree.helper";

interface MergeFoldersDialogContentProps {
  folderId: string;
  currentFolderId?: string | null;
}

export function MergeFoldersDialogContent({ currentFolderId, folderId }: MergeFoldersDialogContentProps) {
  const { setSelectedFolder, clearSelectedFolder } = useFolderListActions();
  const selectedFolder = useSelectedFolder();
  const { useHierarchicalFoldersQuery, mergeFoldersMutation } = useFolderCRUD();
  const { data: hierarchicalFolders, isLoading, error, refetch } = useHierarchicalFoldersQuery();
  const { closeDialog } = useDialogActions();

  const filteredFolders = hierarchicalFolders && Array.isArray(hierarchicalFolders) && folderId
    ? filterHierarchicalFolders(hierarchicalFolders, folderId)
    : hierarchicalFolders || [];

  const handleFolderSelect = (folderId: string | null) => {
    setSelectedFolder(folderId);
  };

  const renderActionButton = (targetFolderId: string) => {
    const handleMerge = () => {
      const destinationFolderId = targetFolderId === "root" ? null : targetFolderId;

      mergeFoldersMutation.mutate({
        folderId: destinationFolderId,
        folderIdToMerge: folderId,
      }, {
        onSuccess: () => {
          clearSelectedFolder();
        }
      });
    };

    return (
      <Button
        className="ml-2 uppercase !bg-foreground !text-background !font-bold disabled:opacity-60 hover:opacity-80"
        icon={<Check size={16} />}
        iconPosition="right"
        onClick={handleMerge}
        disabled={mergeFoldersMutation.isPending}
        loading={mergeFoldersMutation.isPending}
        size="sm"
      >
        Mesclar
      </Button>
    );
  };

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center py-8 space-y-4">
        <AiOutlineLoading3Quarters className="size-8 animate-spin" />
        <Text variant="body-md">Carregando pastas...</Text>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center py-8 space-y-4">
        <X className="size-8 text-primary" />
        <Text variant="body-md" className="text-center">
          Erro ao carregar pastas: {error.message}
        </Text>
        <Button
          size="sm"
          onClick={() => refetch()}
          className="uppercase"
        >
          Tentar novamente
        </Button>
      </div>
    );
  }

  if (filteredFolders.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-8 space-y-4">
        <FaFolder className="size-8 text-muted-foreground" />
        <Text variant="body-md" className="text-center">
          Nenhuma pasta disponível para mesclar
        </Text>
      </div>
    );
  }

  return (
    <div className="space-y-4 min-h-[300px]">
      <div className="flex flex-col gap-4 py-1">
        <div className="flex items-center gap-3 p-4 bg-amber-500/10 border border-amber-500 rounded-sm">
          <AlertTriangle className="size-5 text-white flex-shrink-0" />
          <div>
            <Text variant="body-md" className="font-semibold">
              Todo o conteúdo da pasta atual será movido para a pasta selecionada.
            </Text>
          </div>
        </div>
        <Text variant="body-lg">
          Selecione a <span className="font-bold">pasta de destino para mesclar:</span>
        </Text>
      </div>

      <FolderTreeExplorer
        folders={filteredFolders}
        selectedFolderId={selectedFolder}
        onFolderSelect={handleFolderSelect}
        excludeFolderId={folderId}
        showRootOption={false}
        rootOptionLabel="Pasta raiz"
        className="min-h-[300px]"
        renderActionButton={renderActionButton}
        currentItemParentFolderId={currentFolderId}
      />
    </div>
  );
}

export function MergeFoldersDialogFooter() {
  const { closeDialog } = useDialogActions();
  const { clearSelectedFolder } = useFolderListActions();
  const { mergeFoldersMutation } = useFolderCRUD();

  const handleCancel = () => {
    clearSelectedFolder();
    closeDialog();
  };

  return (
    <div className="flex gap-3">
      <Button
        className="uppercase !bg-transparent"
        onClick={handleCancel}
        disabled={mergeFoldersMutation.isPending}
      >
        Cancelar
      </Button>
    </div>
  );
}

export const MergeFoldersDialog = {
  Content: MergeFoldersDialogContent,
  Footer: MergeFoldersDialogFooter,
};
