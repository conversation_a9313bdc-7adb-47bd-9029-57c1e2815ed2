# Encryption Key Management - Action-Level Implementation Guide

## Overview

This document describes the new encryption key management system that handles secret key validation and authentication at the action level, not at the page level. The system supports both stored keys (with configurable expiration) and one-time usage scenarios, providing maximum flexibility and security.

## Key Changes

### 1. Secret Key Store (`secretKeyStore.ts`)

**Before:**
- Stored actual secret keys in Zustand store memory
- Used `secretKey` field to hold the actual password
- Mixed memory and storage approaches

**After:**
- Only tracks if a valid stored key exists (`hasValidStoredKey: boolean`)
- All encryption keys are derived from stored data only
- Clean separation between UI state and sensitive data

### 2. New Store Interface

```typescript
interface SecretKeyState {
  hasValidStoredKey: boolean;           // Replaces secretKey
  storageDuration: StorageDuration | null;
  actions: {
    setStoredKey: (key: string, duration: StorageDuration, salt: string) => Promise<boolean>;
    clearStoredKey: () => void;
    checkStoredKeyValidity: () => boolean;
  };
}
```

### 3. Encryption Hook (`useEncryption.ts`)

**Key Changes:**
- Always derives keys from stored data only
- Automatic error handling for expired/missing keys
- Triggers re-authentication when keys are invalid

```typescript
const deriveKey = useCallback(async (): Promise<ArrayBuffer> => {
  // Always try to get from storage first
  const storedKey = getStoredEncryptionKey();
  if (!storedKey) {
    // No valid key found - trigger re-authentication
    setIsVerified(false);
    throw new Error("No valid stored key - user needs to re-authenticate");
  }
  return base64ToArrayBuffer(storedKey);
}, [setIsVerified]);
```

## New Hooks for Action-Level Encryption

### 1. `useSecureEncryption` Hook

This is the main hook to use for all encryption operations. It automatically handles key validation and prompts for re-authentication when needed.

```typescript
import { useSecureEncryption } from "~/hooks/useSecureEncryption";

const MyComponent = () => {
  const { encryptData, decryptData, encryptNgrams } = useSecureEncryption();

  const handleEncryptedAction = async () => {
    // This will automatically prompt for secret key if needed
    const result = await encryptData(myData);

    if (!result) {
      // User cancelled authentication
      return;
    }

    if (result.success) {
      // Use encrypted data
      console.log(result.data);
    }
  };

  return (
    <Button onClick={handleEncryptedAction}>
      Perform Encrypted Action
    </Button>
  );
};
```

### 2. `useSecretKeyValidation` Hook

Lower-level hook for custom validation scenarios:

```typescript
import { useSecretKeyValidation } from "~/hooks/useSecretKeyValidation";

const MyComponent = () => {
  const { ensureValidKey } = useSecretKeyValidation();

  const handleCustomAction = async () => {
    const hasValidKey = await ensureValidKey();
    if (!hasValidKey) {
      // User cancelled authentication
      return;
    }

    // Proceed with action that requires valid key
    performCustomOperation();
  };
};
```

## Secret Key Dialog Changes

### SecretKeyDialogContent
- Uses local state instead of store for password input
- Stores current password in `window.__currentSecretKey` for footer access
- No longer persists passwords in memory

### SecretKeyDialogFooter
- Retrieves password from window object when needed
- Uses `setStoredKey()` instead of `setSecretKey()`
- Properly handles storage duration selection

## Automatic Key Management

### SecretKeyInitializer Component
- Checks key validity on app startup and user data changes
- Periodic validation every 30 seconds
- Automatic cleanup of expired keys
- Sets `isVerified` state based on key validity

### Key Expiration Handling
- Automatic detection of expired keys during encryption/decryption
- Immediate re-authentication prompts
- Clean state management

## Implementation Examples

### 1. Action Button with Validation

```typescript
// In ReportCard.tsx
const handleRetryReport = async () => {
  // Validate stored key before proceeding with encryption
  if (!requireValidKey()) {
    // User will be prompted to re-authenticate via SecretKeyInitializer
    return;
  }

  // Proceed with encrypted operation
  const encryptedValue = await encryptData(report.searchArgs);
  // ... rest of the logic
};
```

### 2. Component with Encryption Operations

```typescript
const MyEncryptedComponent = () => {
  const { requireValidKey } = useSecretKeyValidation();
  const { encryptData } = useEncryption();

  const handleSave = async () => {
    if (!requireValidKey()) return;
    
    const encrypted = await encryptData(formData);
    if (encrypted.success) {
      // Save encrypted data
    }
  };

  const handleLoad = async () => {
    if (!requireValidKey()) return;
    
    const decrypted = await decryptData(encryptedData);
    if (decrypted.success) {
      // Use decrypted data
    }
  };

  return (
    <div>
      <Button onClick={handleSave}>Save</Button>
      <Button onClick={handleLoad}>Load</Button>
    </div>
  );
};
```

## Key Features

1. **Action-Level Authentication**: Secret key dialogs are triggered only when needed, not at page load
2. **Flexible Storage Options**: Users can choose to store keys or use them one-time only
3. **Automatic Expiration Handling**: Keys are validated on each use and cleared when expired
4. **Enhanced Security**: No sensitive data in memory longer than necessary
5. **Simple Integration**: Single hook replaces all encryption operations
6. **User-Friendly**: Non-intrusive authentication flow
7. **Clean Architecture**: Clear separation between UI state and encryption logic

## Storage Options

The system supports two modes:

### 1. Stored Keys
- User selects storage duration (1m, 15m, 30m, 1h, session)
- Key is stored in localStorage/sessionStorage with expiration
- Automatic validation on each use
- Cleared when expired

### 2. One-Time Usage
- User selects "Não armazenar" (Don't store)
- Key is used only for the current action
- Automatically cleared after use
- Maximum security for sensitive operations

## Migration Guide

### For Existing Components

1. **Replace `useSecretKey()` calls:**
   ```typescript
   // Before
   const secretKey = useSecretKey();
   
   // After - use local state or validation hook
   const { requireValidKey } = useSecretKeyValidation();
   ```

2. **Add validation to encrypted actions:**
   ```typescript
   // Before
   const handleAction = async () => {
     const result = await encryptData(data);
     // ...
   };
   
   // After
   const handleAction = async () => {
     if (!requireValidKey()) return;
     const result = await encryptData(data);
     // ...
   };
   ```

3. **Update dialog components:**
   - Use local state for password inputs
   - Use `setStoredKey()` instead of `setSecretKey()`

## Utility Function

The `ensureValidStoredKey()` function can be used anywhere in the app to check key validity:

```typescript
import { ensureValidStoredKey } from "~/store/secretKeyStore";

// Direct usage (updates store state automatically)
const isValid = ensureValidStoredKey();
if (!isValid) {
  // Handle re-authentication
}
```

This implementation provides a robust, secure, and user-friendly encryption key management system that automatically handles key expiration and re-authentication while maintaining optimal performance.
