# Encryption Key Management - Implementation Guide

## Overview

This document describes the new encryption key management system that stores secret keys only in localStorage/sessionStorage with configurable expiration times, eliminating in-memory storage for enhanced security.

## Key Changes

### 1. Secret Key Store (`secretKeyStore.ts`)

**Before:**
- Stored actual secret keys in Zustand store memory
- Used `secretKey` field to hold the actual password
- Mixed memory and storage approaches

**After:**
- Only tracks if a valid stored key exists (`hasValidStoredKey: boolean`)
- All encryption keys are derived from stored data only
- Clean separation between UI state and sensitive data

### 2. New Store Interface

```typescript
interface SecretKeyState {
  hasValidStoredKey: boolean;           // Replaces secretKey
  storageDuration: StorageDuration | null;
  actions: {
    setStoredKey: (key: string, duration: StorageDuration, salt: string) => Promise<boolean>;
    clearStoredKey: () => void;
    checkStoredKeyValidity: () => boolean;
  };
}
```

### 3. Encryption Hook (`useEncryption.ts`)

**Key Changes:**
- Always derives keys from stored data only
- Automatic error handling for expired/missing keys
- Triggers re-authentication when keys are invalid

```typescript
const deriveKey = useCallback(async (): Promise<ArrayBuffer> => {
  // Always try to get from storage first
  const storedKey = getStoredEncryptionKey();
  if (!storedKey) {
    // No valid key found - trigger re-authentication
    setIsVerified(false);
    throw new Error("No valid stored key - user needs to re-authenticate");
  }
  return base64ToArrayBuffer(storedKey);
}, [setIsVerified]);
```

## New Utility Hook: `useSecretKeyValidation`

This hook provides a simple way to validate stored keys before performing encrypted operations throughout the app.

### Usage Example

```typescript
import { useSecretKeyValidation } from "~/hooks/useSecretKeyValidation";

const MyComponent = () => {
  const { requireValidKey } = useSecretKeyValidation();

  const handleEncryptedAction = async () => {
    // Validate key before proceeding
    if (!requireValidKey()) {
      // User will be automatically prompted to re-authenticate
      return;
    }
    
    // Proceed with encrypted operation
    const result = await encryptData(myData);
    // ... handle result
  };

  return (
    <Button onClick={handleEncryptedAction}>
      Perform Encrypted Action
    </Button>
  );
};
```

### Available Functions

- **`requireValidKey()`**: Use before any encrypted action. Returns `true` if valid key exists, `false` if re-authentication needed.
- **`validateStoredKey()`**: Lower-level validation function that also updates store state.

## Secret Key Dialog Changes

### SecretKeyDialogContent
- Uses local state instead of store for password input
- Stores current password in `window.__currentSecretKey` for footer access
- No longer persists passwords in memory

### SecretKeyDialogFooter
- Retrieves password from window object when needed
- Uses `setStoredKey()` instead of `setSecretKey()`
- Properly handles storage duration selection

## Automatic Key Management

### SecretKeyInitializer Component
- Checks key validity on app startup and user data changes
- Periodic validation every 30 seconds
- Automatic cleanup of expired keys
- Sets `isVerified` state based on key validity

### Key Expiration Handling
- Automatic detection of expired keys during encryption/decryption
- Immediate re-authentication prompts
- Clean state management

## Implementation Examples

### 1. Action Button with Validation

```typescript
// In ReportCard.tsx
const handleRetryReport = async () => {
  // Validate stored key before proceeding with encryption
  if (!requireValidKey()) {
    // User will be prompted to re-authenticate via SecretKeyInitializer
    return;
  }

  // Proceed with encrypted operation
  const encryptedValue = await encryptData(report.searchArgs);
  // ... rest of the logic
};
```

### 2. Component with Encryption Operations

```typescript
const MyEncryptedComponent = () => {
  const { requireValidKey } = useSecretKeyValidation();
  const { encryptData } = useEncryption();

  const handleSave = async () => {
    if (!requireValidKey()) return;
    
    const encrypted = await encryptData(formData);
    if (encrypted.success) {
      // Save encrypted data
    }
  };

  const handleLoad = async () => {
    if (!requireValidKey()) return;
    
    const decrypted = await decryptData(encryptedData);
    if (decrypted.success) {
      // Use decrypted data
    }
  };

  return (
    <div>
      <Button onClick={handleSave}>Save</Button>
      <Button onClick={handleLoad}>Load</Button>
    </div>
  );
};
```

## Benefits

1. **Enhanced Security**: No sensitive data in memory longer than necessary
2. **Automatic Re-authentication**: Users are prompted when keys expire
3. **Consistent State**: Single source of truth for authentication status
4. **Simple Integration**: Easy-to-use validation hook for action buttons
5. **Performance**: Minimal overhead with efficient key validation
6. **Clean Architecture**: Clear separation between UI state and encryption logic

## Migration Guide

### For Existing Components

1. **Replace `useSecretKey()` calls:**
   ```typescript
   // Before
   const secretKey = useSecretKey();
   
   // After - use local state or validation hook
   const { requireValidKey } = useSecretKeyValidation();
   ```

2. **Add validation to encrypted actions:**
   ```typescript
   // Before
   const handleAction = async () => {
     const result = await encryptData(data);
     // ...
   };
   
   // After
   const handleAction = async () => {
     if (!requireValidKey()) return;
     const result = await encryptData(data);
     // ...
   };
   ```

3. **Update dialog components:**
   - Use local state for password inputs
   - Use `setStoredKey()` instead of `setSecretKey()`

## Utility Function

The `ensureValidStoredKey()` function can be used anywhere in the app to check key validity:

```typescript
import { ensureValidStoredKey } from "~/store/secretKeyStore";

// Direct usage (updates store state automatically)
const isValid = ensureValidStoredKey();
if (!isValid) {
  // Handle re-authentication
}
```

This implementation provides a robust, secure, and user-friendly encryption key management system that automatically handles key expiration and re-authentication while maintaining optimal performance.
