import { useEffect } from "react";
import { useAuth } from "~/hooks/useAuth";
import { useSecretKeyActions } from "~/store/secretKeyStore";
import { eventBus } from "~/helpers/eventBus.helper";

export const AuthEventHandler = () => {
  const { logoutMutation } = useAuth();
  const { clearStoredKey } = useSecretKeyActions();

  useEffect(() => {
    const unsubscribe = eventBus.subscribe("UNAUTHORIZED_ERROR", async () => {
      try {
        console.log("[AuthEventHandler] Handling UNAUTHORIZED_ERROR event - starting logout");
        clearStoredKey(); // Limpa chaves armazenadas antes do logout
        await logoutMutation.mutateAsync();
        console.log("[AuthEventHandler] Logout completed successfully");
        eventBus.resetLogoutFlag();
      } catch (error) {
        console.error("[AuthEventHandler] Logout mutation failed:", error);
        eventBus.resetLogoutFlag();
      }
    });

    return () => {
      unsubscribe();
    };
  }, [logoutMutation, clearStoredKey]);

  return null;
};
