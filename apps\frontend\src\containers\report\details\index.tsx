import { useEffect } from "react";
import { Loading } from "@snap/design-system";
import { Navigate, useParams } from "react-router";
import { useUserData } from "~/store/userStore";
import {
  useReportSections,
  useReportDetailActions,
  useReportMetadata,
  useReportProfileImage,
  useReportType,
  useReportReloadTrigger
}
  from "~/store/reportDetailStore";
import { useReportActionsWithAutoSave } from "~/hooks/useReportActionsWithAutoSave";
import { ReportMetadata, ReportSection, OutletContextType } from "~/types/global";
import { __PersonDetailsPage } from "root/modules/@snap/reports/ui/containers/report/";
import TabContainer from "../TabContainer";
import { toast } from "sonner";
import { useReportCRUD } from "~/hooks/useReportCRUD";
import { usePermissionCheck } from "~/components/router/PermissionGuard";
import { Permission } from "~/helpers/permissions.helper";

const ReportDetailsContainer = () => {
  const { setMetadata, setReportSections, setReportType } = useReportDetailActions();
  const reportSections = useReportSections();
  const metadata = useReportMetadata();
  const profileImage = useReportProfileImage();
  const reportType = useReportType();
  const userData = useUserData();
  const { id, type } = useParams<{ type: string; id: string }>();
  const actions = useReportActionsWithAutoSave()
  const { reportDetailsQuery } = useReportCRUD();
  const reloadTrigger = useReportReloadTrigger();
  const { checkPermission } = usePermissionCheck();
  const canUpdateReport = checkPermission(Permission.UPDATE_REPORT);
  const organizationLogo = userData?.organization_logo as string;
  const { data: reportDetailsData, isLoading, isError, error } = reportDetailsQuery(id!);
  const shouldPrintSnapLogo = userData?.print_snap_logo as boolean;

  useEffect(() => {
    const loadReportData = () => {
      if (!reportDetailsData || isLoading) return;

      const { data: dataMap, ...meta } = reportDetailsData;
      const typeKey = type as keyof typeof dataMap;
      const foundSections: ReportSection[] = dataMap?.[typeKey] || [];

      setReportSections(foundSections);
      //@ts-ignore
      setMetadata(meta);
      setReportType(String(typeKey));
    };

    loadReportData();
  }, [reportDetailsData, isLoading, reloadTrigger]);



  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center h-screen w-full max-h-[calc(100vh-200px)]">
          <Loading size="lg" />
        </div>
      );
    }

    if (isError) {
      console.error("Error fetching report:", error);
      toast.error("Ocorreu um erro ao tentar carregar o relatório");
      return <Navigate to="/" replace />;
    };

    return (
      <div className={`flex px-8 flex-col lg:flex-row gap-8 w-full opacity-0 ${!isLoading && "opacity-100"} transition-opacity duration-200`}>
        <div className="flex-1 md:flex-1/4">
          <TabContainer />
        </div>
        <div className="flex-1 md:flex-3/4 max-h-[calc(100vh-200px)] overflow-y-auto overflow-x-hidden [scrollbar-gutter:stable]" id="report-content">
          <__PersonDetailsPage
            store={{
              sections: reportSections,
              metadata: metadata as ReportMetadata,
              image: profileImage as string,
              organizationLogo: organizationLogo,
              shouldPrintSnapLogo: shouldPrintSnapLogo,
              reportType: reportType,
              isTrashEnabled: canUpdateReport,
              isPrintEnabled: true,
              actions
            }}
          />
        </div>
      </div>
    )
  }

  return renderContent();
};

export default ReportDetailsContainer;
